# 双人弹幕躲避游戏

一个使用Python和Pygame开发的双人同屏弹幕躲避游戏。

## 游戏特色

- **双人同屏对战**: 两名玩家在同一屏幕上竞技
- **复杂弹幕系统**: 包含6种不同的弹幕模式
- **实时切换**: 弹幕模式会自动循环切换
- **简单控制**: 使用键盘WASD和方向键控制

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装pygame：

```bash
pip install pygame
```

## 运行游戏

```bash
python bullet_hell_game.py
```

## 游戏控制

### 玩家1 (蓝色)
- **W**: 向上移动
- **S**: 向下移动
- **A**: 向左移动
- **D**: 向右移动

### 玩家2 (红色)
- **↑**: 向上移动
- **↓**: 向下移动
- **←**: 向左移动
- **→**: 向右移动

### 其他控制
- **R**: 游戏结束后重新开始
- **ESC**: 退出游戏

## 弹幕模式

游戏包含6种不同的弹幕模式，会自动循环切换：

1. **螺旋模式**: 从中心点发射螺旋状弹幕
2. **波浪模式**: 从顶部发射波浪形弹幕
3. **圆形爆发**: 随机位置的圆形爆发弹幕
4. **十字模式**: 十字形和对角线弹幕
5. **随机雨**: 从顶部随机下落的弹幕雨
6. **花朵模式**: 花瓣形状的复杂弹幕

## 游戏规则

- 避免被弹幕击中
- 最后存活的玩家获胜
- 如果两名玩家同时被击中则为平局
- 被击中后游戏结束，可按R重新开始

## 技术特点

- 使用Pygame进行图形渲染
- 60FPS流畅游戏体验
- 碰撞检测系统
- 多种数学函数生成复杂弹幕模式
- 实时状态显示

## 系统要求

- Python 3.6+
- Pygame 2.0.0+
- 支持键盘输入的系统

享受游戏吧！
