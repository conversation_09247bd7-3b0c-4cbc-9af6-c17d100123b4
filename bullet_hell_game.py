import pygame
import math
import random
import sys

# 初始化pygame
pygame.init()

# 游戏常量
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
PURPLE = (255, 0, 255)
CYAN = (0, 255, 255)
ORANGE = (255, 165, 0)

class Player:
    def __init__(self, x, y, color, controls):
        self.x = x
        self.y = y
        self.radius = 8
        self.color = color
        self.speed = 5
        self.controls = controls  # 控制键字典
        self.alive = True
        self.invulnerable_time = 0
        
    def update(self, keys):
        if not self.alive:
            return
            
        # 减少无敌时间
        if self.invulnerable_time > 0:
            self.invulnerable_time -= 1
            
        # 移动控制
        if keys[self.controls['up']] and self.y > self.radius:
            self.y -= self.speed
        if keys[self.controls['down']] and self.y < SCREEN_HEIGHT - self.radius:
            self.y += self.speed
        if keys[self.controls['left']] and self.x > self.radius:
            self.x -= self.speed
        if keys[self.controls['right']] and self.x < SCREEN_WIDTH - self.radius:
            self.x += self.speed
    
    def draw(self, screen):
        if not self.alive:
            return
            
        # 无敌时闪烁效果
        if self.invulnerable_time > 0 and self.invulnerable_time % 10 < 5:
            return
            
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.radius)
        pygame.draw.circle(screen, WHITE, (int(self.x), int(self.y)), self.radius, 2)
    
    def get_rect(self):
        return pygame.Rect(self.x - self.radius, self.y - self.radius, 
                          self.radius * 2, self.radius * 2)

class Bullet:
    def __init__(self, x, y, dx, dy, color=RED, size=3):
        self.x = x
        self.y = y
        self.dx = dx
        self.dy = dy
        self.color = color
        self.size = size
        self.active = True
        
    def update(self):
        self.x += self.dx
        self.y += self.dy
        
        # 移除屏幕外的子弹
        if (self.x < -50 or self.x > SCREEN_WIDTH + 50 or 
            self.y < -50 or self.y > SCREEN_HEIGHT + 50):
            self.active = False
    
    def draw(self, screen):
        if self.active:
            pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)
    
    def get_rect(self):
        return pygame.Rect(self.x - self.size, self.y - self.size, 
                          self.size * 2, self.size * 2)

class BulletPattern:
    def __init__(self):
        self.bullets = []
        self.pattern_timer = 0
        self.current_pattern = 0
        self.pattern_duration = 300  # 每个模式持续时间
        
    def update(self):
        self.pattern_timer += 1
        
        # 切换弹幕模式
        if self.pattern_timer >= self.pattern_duration:
            self.pattern_timer = 0
            self.current_pattern = (self.current_pattern + 1) % 6
        
        # 生成不同的弹幕模式
        if self.current_pattern == 0:
            self.spiral_pattern()
        elif self.current_pattern == 1:
            self.wave_pattern()
        elif self.current_pattern == 2:
            self.circle_burst()
        elif self.current_pattern == 3:
            self.cross_pattern()
        elif self.current_pattern == 4:
            self.random_shower()
        elif self.current_pattern == 5:
            self.flower_pattern()
        
        # 更新所有子弹
        self.bullets = [bullet for bullet in self.bullets if bullet.active]
        for bullet in self.bullets:
            bullet.update()
    
    def spiral_pattern(self):
        if self.pattern_timer % 3 == 0:
            center_x, center_y = SCREEN_WIDTH // 2, 100
            angle = self.pattern_timer * 0.1
            for i in range(8):
                bullet_angle = angle + i * math.pi / 4
                speed = 3
                dx = math.cos(bullet_angle) * speed
                dy = math.sin(bullet_angle) * speed
                self.bullets.append(Bullet(center_x, center_y, dx, dy, PURPLE))
    
    def wave_pattern(self):
        if self.pattern_timer % 5 == 0:
            for i in range(0, SCREEN_WIDTH, 60):
                wave_y = 50 + 30 * math.sin(i * 0.01 + self.pattern_timer * 0.1)
                self.bullets.append(Bullet(i, wave_y, 0, 4, CYAN))
    
    def circle_burst(self):
        if self.pattern_timer % 60 == 0:
            center_x = random.randint(100, SCREEN_WIDTH - 100)
            center_y = random.randint(100, 300)
            for i in range(16):
                angle = i * math.pi / 8
                speed = 2.5
                dx = math.cos(angle) * speed
                dy = math.sin(angle) * speed
                self.bullets.append(Bullet(center_x, center_y, dx, dy, YELLOW))
    
    def cross_pattern(self):
        if self.pattern_timer % 8 == 0:
            center_x, center_y = SCREEN_WIDTH // 2, 50
            # 十字形弹幕
            directions = [(0, 1), (1, 0), (-1, 0), (0.7, 0.7), (-0.7, 0.7), (0.7, -0.7), (-0.7, -0.7)]
            for dx, dy in directions:
                speed = 3.5
                self.bullets.append(Bullet(center_x, center_y, dx * speed, dy * speed, GREEN))
    
    def random_shower(self):
        if self.pattern_timer % 2 == 0:
            x = random.randint(0, SCREEN_WIDTH)
            y = 0
            dx = random.uniform(-2, 2)
            dy = random.uniform(2, 5)
            color = random.choice([RED, ORANGE, PURPLE, CYAN])
            self.bullets.append(Bullet(x, y, dx, dy, color))
    
    def flower_pattern(self):
        if self.pattern_timer % 10 == 0:
            center_x, center_y = SCREEN_WIDTH // 2, 150
            petals = 6
            for i in range(petals):
                angle = (self.pattern_timer * 0.05) + i * 2 * math.pi / petals
                radius = 100 + 50 * math.sin(self.pattern_timer * 0.02)
                x = center_x + radius * math.cos(angle)
                y = center_y + radius * math.sin(angle)
                
                # 向中心发射
                target_angle = math.atan2(center_y - y, center_x - x)
                speed = 2
                dx = math.cos(target_angle) * speed
                dy = math.sin(target_angle) * speed
                self.bullets.append(Bullet(x, y, dx, dy, ORANGE, 4))
    
    def draw(self, screen):
        for bullet in self.bullets:
            bullet.draw(screen)

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("双人弹幕躲避游戏")
        self.clock = pygame.time.Clock()
        
        # 创建玩家
        player1_controls = {
            'up': pygame.K_w,
            'down': pygame.K_s,
            'left': pygame.K_a,
            'right': pygame.K_d
        }
        player2_controls = {
            'up': pygame.K_UP,
            'down': pygame.K_DOWN,
            'left': pygame.K_LEFT,
            'right': pygame.K_RIGHT
        }
        
        self.player1 = Player(300, SCREEN_HEIGHT - 100, BLUE, player1_controls)
        self.player2 = Player(900, SCREEN_HEIGHT - 100, RED, player2_controls)
        
        self.bullet_pattern = BulletPattern()
        self.font = pygame.font.Font(None, 36)
        self.game_over = False
        self.winner = None
        
    def check_collisions(self):
        for bullet in self.bullet_pattern.bullets:
            if not bullet.active:
                continue
                
            # 检查玩家1碰撞
            if (self.player1.alive and self.player1.invulnerable_time == 0 and
                self.player1.get_rect().colliderect(bullet.get_rect())):
                self.player1.alive = False
                if self.player2.alive:
                    self.winner = "玩家2获胜!"
                    self.game_over = True
                elif not self.player2.alive:
                    self.winner = "平局!"
                    self.game_over = True
            
            # 检查玩家2碰撞
            if (self.player2.alive and self.player2.invulnerable_time == 0 and
                self.player2.get_rect().colliderect(bullet.get_rect())):
                self.player2.alive = False
                if self.player1.alive:
                    self.winner = "玩家1获胜!"
                    self.game_over = True
                elif not self.player1.alive:
                    self.winner = "平局!"
                    self.game_over = True
    
    def draw_ui(self):
        # 绘制玩家状态
        status1 = "玩家1 (WASD): " + ("存活" if self.player1.alive else "死亡")
        status2 = "玩家2 (方向键): " + ("存活" if self.player2.alive else "死亡")
        
        text1 = self.font.render(status1, True, BLUE if self.player1.alive else WHITE)
        text2 = self.font.render(status2, True, RED if self.player2.alive else WHITE)
        
        self.screen.blit(text1, (10, 10))
        self.screen.blit(text2, (10, 50))
        
        # 显示当前弹幕模式
        patterns = ["螺旋", "波浪", "圆形爆发", "十字", "随机雨", "花朵"]
        pattern_text = f"弹幕模式: {patterns[self.bullet_pattern.current_pattern]}"
        pattern_surface = self.font.render(pattern_text, True, WHITE)
        self.screen.blit(pattern_surface, (10, 90))
        
        if self.game_over:
            winner_text = self.font.render(self.winner, True, YELLOW)
            restart_text = self.font.render("按R重新开始，按ESC退出", True, WHITE)
            
            winner_rect = winner_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
            restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
            
            self.screen.blit(winner_text, winner_rect)
            self.screen.blit(restart_text, restart_rect)
    
    def reset_game(self):
        self.player1 = Player(300, SCREEN_HEIGHT - 100, BLUE, self.player1.controls)
        self.player2 = Player(900, SCREEN_HEIGHT - 100, RED, self.player2.controls)
        self.bullet_pattern = BulletPattern()
        self.game_over = False
        self.winner = None
    
    def run(self):
        running = True
        
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_r and self.game_over:
                        self.reset_game()
            
            if not self.game_over:
                keys = pygame.key.get_pressed()
                
                # 更新玩家
                self.player1.update(keys)
                self.player2.update(keys)
                
                # 更新弹幕
                self.bullet_pattern.update()
                
                # 检查碰撞
                self.check_collisions()
            
            # 绘制
            self.screen.fill(BLACK)
            
            self.player1.draw(self.screen)
            self.player2.draw(self.screen)
            self.bullet_pattern.draw(self.screen)
            self.draw_ui()
            
            pygame.display.flip()
            self.clock.tick(FPS)
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
